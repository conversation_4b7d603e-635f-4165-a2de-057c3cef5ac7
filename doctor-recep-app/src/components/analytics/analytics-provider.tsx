'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { initializeAnalytics, trackPageView } from '@/lib/analytics'

/**
 * Analytics Provider Component
 * Handles client-side analytics initialization and page tracking
 * Respects the two-zone security model
 */
export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  useEffect(() => {
    // Initialize analytics on mount
    initializeAnalytics()
  }, [])

  useEffect(() => {
    // Track page changes
    trackPageView(document.title)
  }, [pathname])

  return <>{children}</>
}
